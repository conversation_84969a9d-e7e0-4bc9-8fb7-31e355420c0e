<template>
  <BaseItem title="尺寸">
    <n-flex :wrap="false">
      <n-input-number
        size="small"
        :min="0"
        :show-button="false"
        @update:value="updateSize('width', $event)"
      />
      <n-input-number
        size="small"
        :min="0"
        :show-button="false"
        @update:value="updateSize('height', $event)"
      />
    </n-flex>
  </BaseItem>
  <!-- <FlexLayout @update:value="updateLayout" /> -->
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

import { useDataStore } from "@/stores";
import type { INode, INodeAlign } from "@/types";
import { updateNode } from "@/utils/http/apis";
import { formatObject } from "@/utils/tools";

import BaseItem from "../../Item/index.vue";
import FlexLayout from "../../Item/Node/FlexLayout.vue";

const dataStore = useDataStore();
const style = ref<Record<string, any>>({});

watch(
  () => dataStore.currentNode?.nodeId,
  (val) => {
    if (!val) return;
    style.value = formatObject(dataStore.currentNode?.nodeStyles);
  }
);

const updateNodeAttribute = () => {
  updateNode(dataStore.nodesSelected);
};

const updateBindLink = (
  node: INode,
  {
    right,
    bottom,
    offsetWidth,
    offsetHeight
  }: {
    right: number;
    bottom: number;
    offsetWidth: number;
    offsetHeight: number;
  }
) => {
  console.log(offsetWidth, offsetHeight);

  const { targets, sources } = node;
  // 判断连线的起始点或者结束的点是否跟节点的右边或者底边吸附
  targets.forEach((link) => {
    const lastIndex = link.points.length - 1;
    // 初始点吸附在右边
    if ("x" in link.points[0] && link.points[0].x === right) {
      link.points[0].x += offsetWidth;
    }
    // 初始点吸附在底边
    if ("y" in link.points[0] && link.points[0].y === bottom) {
      link.points[0].y += offsetHeight;
      if (offsetWidth > 0 && "x" in link.points[0]) {
        link.points[0].x += offsetWidth / 2;
      }
    }

    if ("x" in link.points[lastIndex] && link.points[lastIndex].x === right) {
      link.points[lastIndex].x += offsetWidth;
    }

    if ("y" in link.points[lastIndex] && link.points[lastIndex].y === bottom) {
      link.points[lastIndex].y += offsetHeight;
    }
  });
  sources.forEach((link) => {
    const firstPoint = link.points[0];
    const lastPoint = link.points[link.points.length - 1];
    if ("x" in firstPoint && firstPoint.x === right) {
      firstPoint.x += offsetWidth;
    }
    if ("x" in lastPoint && lastPoint.x === right) {
      lastPoint.x += offsetWidth;
    }
    if ("y" in firstPoint && firstPoint.y === bottom) {
      firstPoint.y += offsetHeight;
    }
    if ("y" in lastPoint && lastPoint.y === bottom) {
      lastPoint.y += offsetHeight;
    }
  });
};
const updateSize = (key: "width" | "height", value: number) => {
  dataStore.nodesSelected.forEach((node) => {
    // const { width, height, x, y } = node;
    // 更新关联的线
    node[key] = value;
    // updateBindLink(node, {
    //   right: x + width,
    //   bottom: y + height,
    //   offsetWidth: node.width - width,
    //   offsetHeight: node.height - height
    // });
  });
  updateNodeAttribute();
};

const updateLayout = (direction: string, value: INodeAlign) => {
  dataStore.nodesSelected.forEach((node) => {
    node.style.display = "flex";
    node.style[direction] = value;
    node.nodeStyles = JSON.stringify(node.style);
  });
  updateNodeAttribute();
};
</script>

<style scoped></style>
