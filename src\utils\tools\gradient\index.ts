/**
 * 渐变工具函数
 */

// 颜色停止点接口
export interface ColorStop {
  color: string;
  offset: number;
}

// 渐变数据接口
export interface GradientData {
  direction: number;
  repeat?: string;
  stops: ColorStop[];
  css?: string;
}

/**
 * 将角度转换为SVG线性渐变坐标
 * @param angle 角度 (0-360)
 * @returns SVG坐标对象
 */
export function angleToSvgCoords(angle: number) {
  const radians = (angle * Math.PI) / 180;

  // 计算起点和终点坐标 (以百分比表示)
  const x1 = 50 - 50 * Math.cos(radians);
  const y1 = 50 - 50 * Math.sin(radians);
  const x2 = 50 + 50 * Math.cos(radians);
  const y2 = 50 + 50 * Math.sin(radians);

  return {
    x1: `${x1}%`,
    y1: `${y1}%`,
    x2: `${x2}%`,
    y2: `${y2}%`
  };
}

/**
 * 生成CSS线性渐变字符串
 * @param direction 方向角度
 * @param stops 颜色停止点数组
 * @param repeat 重复方式
 * @returns CSS渐变字符串
 */
export function generateCssGradient(
  direction: number,
  stops: ColorStop[],
  repeat: string = "no-repeat"
): string {
  const sortedStops = [...stops].sort((a, b) => a.offset - b.offset);
  const stopStrings = sortedStops.map((stop) => `${stop.color} ${stop.offset}%`);

  let gradientFunction = `linear-gradient(${direction}deg, ${stopStrings.join(", ")})`;

  if (repeat !== "no-repeat") {
    gradientFunction = `repeating-${gradientFunction}`;
  }

  return gradientFunction;
}

/**
 * 生成SVG线性渐变定义
 * @param id 渐变ID
 * @param direction 方向角度
 * @param stops 颜色停止点数组
 * @returns SVG渐变定义字符串
 */
export function generateSvgGradient(id: string, direction: number, stops: ColorStop[]): string {
  const coords = angleToSvgCoords(direction);
  const sortedStops = [...stops].sort((a, b) => a.offset - b.offset);

  const stopElements = sortedStops
    .map((stop) => `<stop offset="${stop.offset}%" stop-color="${stop.color}" />`)
    .join("\n    ");

  return `<linearGradient id="${id}" x1="${coords.x1}" y1="${coords.y1}" x2="${coords.x2}" y2="${coords.y2}">
    ${stopElements}
  </linearGradient>`;
}

/**
 * 解析CSS渐变字符串
 * @param cssGradient CSS渐变字符串
 * @returns 渐变数据对象
 */
export function parseCssGradient(cssGradient: string): GradientData | null {
  // 匹配线性渐变
  const linearMatch = cssGradient.match(/linear-gradient\((\d+)deg,\s*(.+)\)/);
  if (!linearMatch) return null;

  const direction = parseInt(linearMatch[1]);
  const stopsStr = linearMatch[2];

  // 解析颜色停止点
  const stops: ColorStop[] = [];
  const stopMatches = stopsStr.split(",");

  stopMatches.forEach((stopStr) => {
    const trimmed = stopStr.trim();
    const parts = trimmed.split(" ");

    if (parts.length >= 2) {
      const color = parts[0];
      const offsetStr = parts[parts.length - 1];
      const offset = parseInt(offsetStr.replace("%", ""));

      if (!isNaN(offset)) {
        stops.push({ color, offset });
      }
    }
  });

  return {
    direction,
    stops,
    css: cssGradient
  };
}

/**
 * 创建默认渐变数据
 * @param startColor 起始颜色
 * @param endColor 结束颜色
 * @param direction 方向角度
 * @returns 渐变数据对象
 */
export function createDefaultGradient(
  startColor: string = "#ff0000",
  endColor: string = "#0000ff",
  direction: number = 0
): GradientData {
  const stops: ColorStop[] = [
    { color: startColor, offset: 0 },
    { color: endColor, offset: 100 }
  ];

  return {
    direction,
    stops,
    css: generateCssGradient(direction, stops)
  };
}

/**
 * 验证颜色停止点数组
 * @param stops 颜色停止点数组
 * @returns 验证后的停止点数组
 */
export function validateColorStops(stops: ColorStop[]): ColorStop[] {
  if (!Array.isArray(stops) || stops.length === 0) {
    return [
      { color: "#ff0000", offset: 0 },
      { color: "#0000ff", offset: 100 }
    ];
  }

  // 确保至少有两个停止点
  if (stops.length === 1) {
    stops.push({ color: "#ffffff", offset: 100 });
  }

  // 验证偏移量范围
  return stops.map((stop) => ({
    ...stop,
    offset: Math.max(0, Math.min(100, stop.offset))
  }));
}

/**
 * 均匀分布颜色停止点
 * @param stops 颜色停止点数组
 * @returns 均匀分布后的停止点数组
 */
export function distributeStopsEvenly(stops: ColorStop[]): ColorStop[] {
  const count = stops.length;
  if (count <= 1) return stops;

  return stops.map((stop, index) => ({
    ...stop,
    offset: +((index / (count - 1)) * 100).toFixed(1)
  }));
}

/**
 * 反转渐变方向
 * @param gradientData 渐变数据
 * @returns 反转后的渐变数据
 */
export function reverseGradient(gradientData: GradientData): GradientData {
  const reversedStops = gradientData.stops
    .map((stop) => ({
      ...stop,
      offset: 100 - stop.offset
    }))
    .reverse();

  return {
    ...gradientData,
    stops: reversedStops,
    css: generateCssGradient(gradientData.direction, reversedStops, gradientData.repeat)
  };
}

/**
 * 添加颜色停止点
 * @param stops 当前停止点数组
 * @param color 新颜色
 * @param offset 新偏移量（可选，自动计算）
 * @returns 新的停止点数组
 */
export function addColorStop(
  stops: ColorStop[],
  color: string = "#ffffff",
  offset?: number
): ColorStop[] {
  const newStops = [...stops];

  if (offset === undefined) {
    // 自动计算偏移量
    const maxOffset = Math.max(...stops.map((s) => s.offset));
    offset = Math.min(100, maxOffset + 10);
  }

  newStops.push({ color, offset });
  return newStops.sort((a, b) => a.offset - b.offset);
}

/**
 * 移除颜色停止点
 * @param stops 当前停止点数组
 * @param index 要移除的索引
 * @returns 新的停止点数组
 */
export function removeColorStop(stops: ColorStop[], index: number): ColorStop[] {
  if (stops.length <= 2) return stops; // 至少保留两个停止点

  const newStops = [...stops];
  newStops.splice(index, 1);
  return newStops;
}

// /**
//  *
//  */
// export
