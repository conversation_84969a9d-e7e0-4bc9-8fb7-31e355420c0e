<template>
  <image :width="data.width" :height="data.height" :style="data.style" :href="getNodeImage(data)" />
  <Text :data="data" v-if="data.nodeText" />
</template>

<script setup lang="ts">
import type { INode } from "@/types";
import { getNodeImage } from "@/utils/tools";

import Text from "./Text.vue";

defineProps<{
  data: INode;
}>();

// const style = computed(() => {
// const bg = props.data.style.image ? `url(${getNodeImage(props.data)})` : props.data.style.fill;
// return {
//   ...props.data.style
// backgroundImage: `url(${getNodeImage(props.data)})`,
// backgroundColor: (props.data.style.fill as string) || "transparent",
// backgroundSize: props.data.style["background-size"],
// backgroundRepeat: "no-repeat",
// backgroundPosition: props.data.style["background-position"],
// width: "100%",
// height: "100%"
// };
// });
</script>
