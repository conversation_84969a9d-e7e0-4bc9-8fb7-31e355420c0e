<template>
  <n-scrollbar style="height: calc(100vh - 210px)" class="pr-3">
    <MapAttribute v-if="isShowMapAttribute"></MapAttribute>
    <template v-else>
      <MultiAttribute
        v-if="dataStore.nodesSelected.length > 1 || dataStore.linksSelected.length > 1"
      ></MultiAttribute>
      <template v-else>
        <NodeAtrribute v-if="dataStore.currentNode"></NodeAtrribute>
        <LinkAttribute v-if="dataStore.currentLink"></LinkAttribute>
      </template>
    </template>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { computed } from "vue";

import { useDataStore, useMapStore } from "@/stores";

import LinkAttribute from "./LinkAttribute/index.vue";
import MapAttribute from "./MapAttribute/index.vue";
import MultiAttribute from "./MultiAttribute/index.vue";
import NodeAtrribute from "./NodeAttribute/index.vue";

const dataStore = useDataStore();
const mapStore = useMapStore();
const isShowMapAttribute = computed(() => {
  return (
    !!mapStore.mapInfo && dataStore.linksSelected.length + dataStore.nodesSelected.length === 0
  );
});
</script>

<style></style>
