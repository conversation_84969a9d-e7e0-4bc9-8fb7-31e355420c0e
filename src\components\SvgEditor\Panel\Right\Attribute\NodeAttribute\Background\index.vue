<template>
  <BackgroundSize
    :value="dataStore.currentNode?.style['background-size']"
    @update:value="updateStyles('background-size', $event)"
  />
  <BackgroundPosition
    :value="dataStore.currentNode?.style['background-position']"
    @update:value="updateStyles('background-position', $event)"
  />
  <BackgroundUrl
    :value="dataStore.currentNode?.style['image']"
    @update:value="updateStyles('image', $event)"
  />
</template>

<script setup lang="ts">
import { useDataStore } from "@/stores";
import { updateNode } from "@/utils/http/apis";

import BackgroundPosition from "../../Item/Background/BackgroundPosition.vue";
import BackgroundSize from "../../Item/Background/BackgroundSize.vue";
import BackgroundUrl from "../../Item/Background/BackgroundUrl.vue";

const dataStore = useDataStore();

const updateNodeAttribute = () => {
  if (!dataStore.currentNode) return;
  updateNode([dataStore.currentNode]);
};

const updateStyles = (key: string, value: string) => {
  if (!dataStore.currentNode) return;

  dataStore.currentNode.style[key] = value;
  dataStore.currentNode.nodeStyles = JSON.stringify(dataStore.currentNode.style);
  updateNodeAttribute();
};
</script>

<style scoped></style>
