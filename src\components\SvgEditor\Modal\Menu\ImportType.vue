<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="导入设置"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh"
  >
    <n-form
      ref="fileFormRef"
      :model="fileModel"
      :rules="fileRules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="名称" path="name">
        <n-input v-model:value="fileModel.name" :max-length="30" placeholder="文件名称" />
      </n-form-item>
      <n-form-item label="方向" path="direction">
        <n-select
          v-model:value="fileModel.direction"
          filterable
          placeholder="请选择指标实例"
          :options="importDirectionOptions"
        />
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { FormInst, FormRules } from "naive-ui";

import type { IImportExcelInfo } from "@/types";

const emit = defineEmits<{
  submit: [IImportExcelInfo];
}>();
let file: File;
const isVisible = ref(false);
const fileModel = ref({
  name: "",
  direction: "LR"
});

const fileFormRef = ref<FormInst | null>(null);

// TB, BT, LR, or RL
const importDirectionOptions = [
  { label: "左右", value: "LR" },
  { label: "上下", value: "TB" },
  { label: "右左", value: "RL" },
  { label: "下上", value: "BT" }
];

const fileRules: FormRules = {
  name: {
    required: true,
    message: "请输入文件名称",
    trigger: ["input", "blur"]
  }
};

const show = (_file: File) => {
  fileModel.value.name = _file.name.split(".")[0];
  file = _file;
  isVisible.value = true;
};

const hide = () => {
  isVisible.value = false;
};

const submit = () => {
  fileFormRef.value?.validate((errors) => {
    if (errors) return;
    emit("submit", { ...fileModel.value, file });
    hide();
  });
  return false;
};

defineExpose({
  show
});
</script>
