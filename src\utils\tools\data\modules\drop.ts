import { useMapStore } from "@/stores";
import type { IMetaItem, IOriginalLink, IOriginalNode, IPosition } from "@/types";
import { BaseLink, BaseNode } from "@/utils/constant";
import { addLink, addNode, formatObject, getTransPosition } from "@/utils/tools";

export const onDroped = (evt: DragEvent) => {
  const val = evt.dataTransfer?.getData("text/plain");
  const svgEl = document.querySelector("#svgEditor") as SVGSVGElement;
  if (!svgEl) return;
  const rect = svgEl.getBoundingClientRect();
  const x1 = evt.x - rect.x;
  const y1 = evt.y - rect.y;

  const [x, y] = getTransPosition(x1, y1);
  const obj = formatObject(val) as unknown as IMetaItem;
  const [node, link] = generateNodeLink(obj, { x, y });

  node && addNode(node);
  link && addLink(link);
};

export const generateNodeLink = (
  val: IMetaItem,
  position: IPosition
): [IOriginalNode | null, IOriginalLink | null] => {
  const mapStore = useMapStore();
  const mapId = mapStore.mapInfo?.mapId || "";
  let node: IOriginalNode | null = null;
  let link: IOriginalLink | null = null;

  if (!mapId) {
    window.$message.error("请先选择图层");
    return [node, link];
  }
  const { x, y } = position;

  if (val.objType === "path") {
    link = {
      ...BaseLink,
      mapId //图层id
    };
  } else {
    const nodeType = val.objType;
    const style: Record<string, any> = {
      fill: ["rect", "circle"].includes(val.objType) ? "#19be6b" : "transparent",
      image: val.groupId === "base" ? "" : val.objImg,
      "background-size": "auto"
    };
    node = {
      ...BaseNode,
      mapId,
      nodeType,
      nodePosition: `${x},${y}`,
      nodeSize: val.objType === "text" ? "120*30" : "100*100",
      nodeStyles: JSON.stringify(style),
      nodeText: val.objType === "text" ? "示例文字" : ""
    };
  }
  return [node, link];
};
