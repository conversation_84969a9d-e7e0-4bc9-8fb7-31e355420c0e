<template>
  <n-card
    class="attribute-panel absolute top-5 right-0 w-90 h-90%"
    :style="translate"
    closable
    @close="closeAttributeView"
  >
    <div class="absolute top-0 left-0 w-full h-full p-2 pr-0">
      <n-tabs v-model:value="tabActiveName" type="line" animated>
        <!-- #suffix 给card的close按钮提供空间显示  -->
        <template #suffix> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </template>
        <n-tab-pane name="attribute" tab="属性">
          <Attribute></Attribute>
        </n-tab-pane>
        <n-tab-pane
          name="layer"
          tab="子图层"
          :disabled="!mapStore.mapInfo"
          display-directive="show"
        >
          <SublayerList
        /></n-tab-pane>
        <n-tab-pane name="data" tab="数据" display-directive="show"> <DataBind /> </n-tab-pane>

        <n-tab-pane name="event" tab="事件">
          <EventBind />
        </n-tab-pane>
        <n-tab-pane name="script" tab="脚本">
          <Script></Script>
        </n-tab-pane>
      </n-tabs>
    </div>
  </n-card>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { useCommonStore, useMapStore } from "@/stores/";

import Attribute from "./Attribute/index.vue";
import DataBind from "./DataBind/index.vue";
import EventBind from "./EventBind/index.vue";
import Script from "./Script/index.vue";
import SublayerList from "./SublayerList/index.vue";

const commonStore = useCommonStore();
const mapStore = useMapStore();

const tabActiveName = ref("attribute");

const translate = computed(() => {
  return {
    transform:
      commonStore.isAttributeViewVisible && mapStore.mapInfo
        ? "translateX(-20px)"
        : "translateX(100%)"
  };
});

const closeAttributeView = () => {
  commonStore.isAttributeViewVisible = false;
};

watch(
  () => mapStore.mapInfo?.mapId,
  () => {
    tabActiveName.value = "attribute";
  }
);
</script>

<style scoped>
.attribute-panel {
  transition: all 0.3s;
  background-color: #181818;
}
</style>
