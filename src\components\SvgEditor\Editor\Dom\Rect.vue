<template>
  <rect class="node-text-rect" :width="data.width" :height="data.height" :style="data.style"></rect>

  <Text :data="data" />
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import * as d3 from "d3";

import type { INode } from "@/types";
import { bindNodeDrag } from "@/utils/editor/event";

import Text from "./Text.vue";

const props = defineProps<{
  data: INode;
}>();

onMounted(() => {
  const node = d3.select<SVGGElement, INode>(`#node_${props.data.nodeId}`).data([props.data]);

  bindNodeDrag(node);
});
</script>

<style></style>
