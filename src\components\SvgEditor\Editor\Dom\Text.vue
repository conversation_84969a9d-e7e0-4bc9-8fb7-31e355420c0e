<template>
  <foreignObject
    :width="data.width"
    :height="data.height"
    :transform="textTransform"
    v-if="isOldData"
  >
    <div style="white-space: pre-wrap" :style="oldTextStyle">
      {{ data.nodeText }}
    </div>
  </foreignObject>

  <text :width="data.width" :height="data.height" :style="style" :transform="textTransform" v-else>
    <tspan v-for="(line, index) in nodeText" :key="index" :x="0" :dy="index === 0 ? '0' : '1.2em'">
      {{ line }}
    </tspan>
  </text>
</template>

<script setup lang="ts">
import { computed } from "vue";

import type { INode } from "@/types";

const props = defineProps<{
  data: INode;
}>();

/**
 * 如果有换行,返回数组 并显示
 */
const nodeText = computed(() => {
  const text = props.data.nodeText;
  if (typeof text === "string" && text.includes("\n")) {
    return text.split("\n");
  }
  return [text || ""];
});

const style = computed(() => {
  const { textStyle, style } = props.data;
  return {
    ...textStyle,
    fill: props.data.fontColor || (style.fill as string),
    fontSize: props.data.fontSize + "px"
  };
});

const textTransform = computed(() => {
  const textStyle = props.data.textStyle;
  return `translate(${textStyle.x || 0}, ${textStyle.y || 0})`;
});

// 判断是否是旧数据
const isOldData = computed(() => {
  return props.data.style.display === "flex";
});

const oldTextStyle = computed(() => {
  const { textStyle, style } = props.data;
  return {
    ...textStyle,
    display: (style.display || "block") as string,
    justifyContent: (style["justify-content"] || "unset") as string,
    alignItems: (style["align-items"] || "unset") as string,
    width: props.data.width + "px",
    height: props.data.height + "px",
    color: props.data.fontColor,
    fontSize: props.data.fontSize + "px"
  };
});
</script>
