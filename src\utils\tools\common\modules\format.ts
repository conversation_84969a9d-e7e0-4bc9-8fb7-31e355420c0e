import type { IPosition } from "@/types";

export const formatObject = (data?: string): Record<string, string> => {
  if (!data) return {};
  if (typeof data === "object") return data;
  console.log("🚀 ~ formatObject ~ data:", data);
  try {
    const obj: Record<string, any> = {};
    const d = JSON.parse(data);
    for (const key in d) {
      obj[key.trim()] = d[key];
    }
    return obj;
  } catch (error) {
    throw new Error(`格式化对象失败:,${error}`);
  }
};

/**
 *
 * @param points
 *  Array<{ x: number; y: number }> -> M x y L x y
 */
export const fomatPath = (points: IPosition[], marginWidth: number) => {
  let path = "";
  points.forEach((point, index) => {
    if (index === 0) {
      path += `M ${point.x + marginWidth} ${point.y + marginWidth} `;
    } else {
      path += `L ${point.x + marginWidth} ${point.y + marginWidth} `;
    }
  });

  return path;
};
