<template>
  <BaseItem title="内容">
    <n-input
      type="textarea"
      :value="dataStore.currentNode?.nodeText"
      size="small"
      placeholder="请输入文字"
      @update:value="updateStringAttr('nodeText', $event)"
    />
  </BaseItem>
  <!-- 字体、大小、颜色、旋转角度 -->
  <BaseItem title="大小">
    <n-input-number
      style="width: 100%"
      :value="Number(dataStore.currentNode?.fontSize)"
      size="small"
      :min="0"
      placeholder="请输入文字大小"
      @update:value="updateStyles('font-size', $event)"
    />
  </BaseItem>
  <FontFamily
    :value="dataStore.currentNode?.textStyle['font-family'] || null"
    @update:value="updateTextStyles('font-family', $event)"
  />

  <FontWeight
    :value="dataStore.currentNode?.textStyle['font-weight'] || null"
    @update:value="updateTextStyles('font-weight', $event)"
  />
  <LineHeight
    :value="dataStore.currentNode?.textStyle['line-height'] || null"
    @update:value="updateTextStyles('line-height', $event)"
  />
  <BaseItem title="颜色">
    <n-color-picker
      :value="dataStore.currentNode?.fontColor"
      size="small"
      :modes="['rgb', 'hex']"
      @update:value="updateStyles('fontColor', $event)"
    />
  </BaseItem>
  <BaseItem title="位置">
    <n-flex :wrap="false">
      <n-input-number
        :value="dataStore.currentNode?.textStyle.x"
        size="small"
        :min="0"
        @update:value="updatePostion('x', $event)"
      />
      <n-input-number
        :value="dataStore.currentNode?.textStyle.y"
        size="small"
        :min="0"
        @update:value="updatePostion('y', $event)"
      />
    </n-flex>
  </BaseItem>
  <!-- <TextAlign @update:value="alignText" /> -->
</template>

<script setup lang="ts">
import { useDataStore } from "@/stores";
import { TextAlignMap } from "@/utils/constant";
import { updateNode } from "@/utils/http/apis";

import BaseItem from "../../Item/index.vue";
import FontFamily from "../../Item/Text/FontFamily.vue";
import FontWeight from "../../Item/Text/FontWeight.vue";
import LineHeight from "../../Item/Text/LineHeight.vue";
// import TextAlign from "../../Item/Text/TextAlign.vue";

const dataStore = useDataStore();

const updateNodeAttribute = () => {
  if (!dataStore.currentNode) return;
  updateNode([dataStore.currentNode]);
};

const updateStringAttr = (type: "compClass" | "nodeText", value: string) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode[type] = value;
  updateNodeAttribute();
};

const updateStyles = (key: string, value: string) => {
  if (!dataStore.currentNode) return;
  if (key === "fontColor") {
    dataStore.currentNode.fontColor = value;
    dataStore.currentNode.textStyle.color = value;
    dataStore.currentNode.textStyles = JSON.stringify(dataStore.currentNode.textStyle);
  } else if (key === "font-size") {
    dataStore.currentNode.fontSize = value;
    dataStore.currentNode.style["font-size"] = value + "px";
  } else {
    dataStore.currentNode.style[key] = value;
  }
  dataStore.currentNode.nodeStyles = JSON.stringify(dataStore.currentNode.style);
  updateNodeAttribute();
};

const updateTextStyles = (key: string, value: string) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode.textStyle[key] = value;
  dataStore.currentNode.textStyles = JSON.stringify(dataStore.currentNode.textStyle);
  updateNodeAttribute();
};

const alignText = (align: "left" | "center" | "end") => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode.textStyle["text-align"] = TextAlignMap[align];
  dataStore.currentNode.textStyles = JSON.stringify(dataStore.currentNode.textStyle);
  updateNodeAttribute();
};

const updatePostion = (key: "x" | "y", value: number) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode.textStyle[key] = value;
  updateNodeAttribute();
};
</script>
