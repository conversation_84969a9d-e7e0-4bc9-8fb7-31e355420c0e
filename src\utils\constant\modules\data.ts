import type { IOriginalLink, IOriginalNode } from "@/types";

export const BaseNode: IOriginalNode = {
  mapId: "",
  domId: "",
  nodeType: "",
  compClass: "",
  nodePosition: "",
  nodeSize: "",
  nodeStyles: `{"fill": "#19be6b","background-size": "auto"}`,
  metaData: {},
  rotate: 0, //旋转角度
  nodeText: "", //节点文字
  fontSize: "16", //节点字号
  fontColor: "#d5d5d5", //节点字色
  textPosition: "", //文字位置
  textStyles: `{"dominant-baseline": "text-before-edge"}`, //文字样式
  bindData: {}, //关联数据
  bindMap: {}, //关联图层
  sublayerList: []
};

export const BaseLink: IOriginalLink = {
  mapId: "",
  domId: "",
  linkType: "",
  metaData: {},
  script: "",
  dashedLink: "",
  compClass: "",
  linkPath: "",
  linkWidth: 5,
  linkStyles: '{"color":"#a1a2a2"}', //线样式
  linkAnimations: { fadeOut: "12" }, //线动效
  fromObj: "",
  endObj: "",
  bindData: {},
  bindMap: {},
  sublayerList: []
};
