<template>
  <n-collapse :default-expanded-names="['base', 'node', 'link']">
    <n-collapse-item title="基础" name="base"><MultiAttribute /> </n-collapse-item>
    <n-collapse-item title="图元" name="metaIcon">
      <BindMeta @update:bindMeta="changeMetaIcon"></BindMeta>
      <CustomItem @update:value="updateBindData"></CustomItem>
      <BackgroundSize @update:value="updateStyles('background-size', $event)" />
      <BackgroundPosition @update:value="updateStyles('background-position', $event)" />
    </n-collapse-item>
    <n-collapse-item title="文字" name="text">
      <TextMultiAttribute />
    </n-collapse-item>
    <n-collapse-item title="节点" name="node"><NodeMultiAttribute /> </n-collapse-item>
    <n-collapse-item title="连线" name="link"><LinkMultiAttribute /> </n-collapse-item>
  </n-collapse>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

import { useDataStore } from "@/stores";
import type { IMetaItem } from "@/types";
import { updateNodesLinks } from "@/utils/http/apis";
import { formatObject } from "@/utils/tools";

import BindMeta from "../Item/BindMeta.vue";
import CustomItem from "../Item/CustomItem.vue";
import BackgroundPosition from "../Item/Background/BackgroundPosition.vue";
import BackgroundSize from "../Item/Background/BackgroundSize.vue";

import MultiAttribute from "./BaseMultiAttribute/index.vue";
import NodeMultiAttribute from "./NodeMultiAttribute/index.vue";
import LinkMultiAttribute from "./LinkMultiAttribute/index.vue";
import TextMultiAttribute from "./TextMultiAttribute/index.vue";

const dataStore = useDataStore();
const style = ref<Record<string, any>>({});

// watch(
//   () => dataStore.currentNode?.nodeId,
//   (val) => {
//     if (!val) return;
//     style.value = formatObject(dataStore.currentNode?.nodeStyles);
//   }
// );

const changeMetaIcon = (value: string, row: IMetaItem) => {
  if (row.compClass === "link") {
    dataStore.linksSelected.forEach((link) => {
      link.linkType = value;
    });
  } else {
    dataStore.nodesSelected.forEach((node) => {
      node.nodeType = value;
      node.style.image = row.objImg;
      node.nodeStyles = JSON.stringify(node.style);
    });
  }

  updateNodeLinkAttribute();
};

const updateBindData = (value: string) => {
  dataStore.nodesSelected.forEach((node) => {
    if (!node.bindData) {
      node.bindData = {};
    }
    node.bindData.nodeType = value;
  });
  updateNodeLinkAttribute();
};

const updateNodeLinkAttribute = async () => {
  await updateNodesLinks({
    nodes: dataStore.nodesSelected,
    links: dataStore.linksSelected
  });
};

const updateStyles = (key: string, value: string) => {
  dataStore.nodesSelected.forEach((node) => {
    node.style[key] = value;
    node.nodeStyles = JSON.stringify(node.style);
  });

  updateNodeLinkAttribute();
};
</script>

<style scoped></style>
