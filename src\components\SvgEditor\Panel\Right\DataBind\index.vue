<template>
  <DataBindItem
    v-for="item in dataStore.groupSelected"
    :key="item.groupId"
    :data="item"
  ></DataBindItem>
  <BaseItem title="ID" v-if="!dataStore.groupSelected.length">
    <!-- <n-input placeholder="唯一ID" size="small" @update:value="onAttributeChange($event, 'domId')" /> -->
    <n-input
      :value="dataStore.currentNode?.domId || dataStore.currentLink?.domId || null"
      placeholder="唯一ID"
      size="small"
      @update:value="onAttributeChange($event, 'domId')"
    />
  </BaseItem>

  <BaseItem title="同步" tooltip="同步ID到事件变量值中" v-if="!dataStore.groupSelected.length">
    <n-button size="small" type="primary" class="ml-1" @click="onSyncId">设置</n-button>
  </BaseItem>
  <div class="fixed top-0 left-0" v-if="commonStore.isDev">
    <svg width="1000" height="500">
      <g id="svgCon"></g>
    </svg>
    <NButton @click="updateSvg">测试</NButton>
  </div>

  <!-- <DataBindModal ref="dataBindModalRef" @onValueUpdate="onValueUpdate"></DataBindModal> -->
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import * as d3 from "d3";
import { useDialog } from "naive-ui";

import DataBindModal from "@/components/Common/Modal/DataBindConfig/index.vue";
import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import DataBindItem from "@/components/SvgEditor/Panel/Right/DataBind/DataBindList.vue";
import { useCommonStore, useDataStore, useMapStore } from "@/stores";
import type { IGroupData, ILink, IMouseEvent, INode } from "@/types";
import { LogicCustomComponent } from "@/utils/components/LogicCustomComponent";
import { updateLink, updateMapGroupData, updateNode, updateNodesLinks } from "@/utils/http/apis";
import { getGroupDataList } from "@/utils/tools";

const dialog = useDialog();

const commonStore = useCommonStore();
const dataStore = useDataStore();
const dataBindModalRef = ref<InstanceType<typeof DataBindModal> | null>(null);

let svg: LogicCustomComponent;

const mapStore = useMapStore();
const currentGroup = ref<IGroupData>();
const cureentType = ref<string>("");

watch(
  () => dataStore.currentNode?.nodeId || dataStore.currentLink?.linkId,
  () => {
    if (!commonStore.isDev) return;
    d3.select("#svgCon").selectAll("*").remove();
    svg = new LogicCustomComponent(
      d3.select("#svgCon"),
      dataStore.currentNode || (dataStore.currentLink as any)
    );
  }
);

const updateSvg = () => {
  setInterval(() => {
    // 生成0-1之间的随机数并转为字符串
    const value = Math.random().toFixed(1);
    //  0 蓝色 1 橙色 2 红色 随机
    const color = Math.floor(Math.random() * 3);

    const data: any = [
      {
        domId: "",
        value: color,
        // dataType: "text",
        column: "tempwarn",
        conditions: [
          {
            tagName: "",
            comparison: "=",
            threshold: 0,
            style: {
              data: "none",
              type: "display"
            }
          },
          {
            tagName: "",
            comparison: "=",
            threshold: 1,
            style: {
              data: "block",
              type: "display"
            }
          },
          {
            tagName: "",
            comparison: "=",
            threshold: 1,
            style: {
              data: "/ftp/icon/船-待停-虚.svg",
              type: "background"
            }
          },
          {
            tagName: "",
            comparison: ">",
            threshold: 5,
            style: {
              data: "/ftp/icon/船-待停-虚.svg",
              type: "background"
            }
          },
          {
            tagName: "",
            comparison: "=",
            threshold: 0,
            style: {
              data: "/ftp/icon/frozen-train-1.webp",
              type: "background"
            }
          }
        ]
      },
      {
        domId: "",
        value,
        // dataType: "text",
        column: "stocknum",
        conditions: []
      },
      {
        domId: "",
        value: 1,
        // dataType: "text",
        column: "maxstocknum",
        conditions: []
      }
    ];
    console.log("🚀 ~ setInterval ~ data:", data);

    svg.update(data);
  }, 1000);
};

const showDataBindModal = (type: string, group: IGroupData) => {
  cureentType.value = type;
  currentGroup.value = group;
  dataBindModalRef.value?.show(true);
};

const onGroupDataBindDelete = (bindDataIndex: number, groupIndex: number) => {
  currentGroup.value = dataStore.groupSelected[groupIndex];
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      currentGroup.value?.bindData?.splice(bindDataIndex, 1);
      await updateGroupAttribute();
      window.$message.success("删除成功");
    },
    onAfterLeave: () => {}
  });

  // updateGroupAttribute();
};

// 更新节点属性
const updateGroupAttribute = async () => {
  if (!currentGroup.value) return;
  const { nodes, links } = currentGroup.value;
  const mapId = mapStore.mapInfo!.mapId;

  await updateMapGroupData({
    ...currentGroup.value,
    mapId,
    topoMapsGroupDataList: getGroupDataList(nodes, links)
  });

  window.$message.success("更新成功");
};

const onValueUpdate = ({ key, detailId }: { key: string | null; detailId: string | null }) => {
  currentGroup.value!.bindData?.forEach((item) => {
    if (item.type === cureentType.value) {
      item.key = key;
      //   item.value = value!;
      item.detailId = detailId;
    }
  });

  updateGroupAttribute();
};

// 更新节点属性
const updateNodeAttributeByHttp = () => {
  if (!dataStore.currentNode) return;
  updateNode([dataStore.currentNode]);
};

const updateLinkAttributeByHttp = () => {
  if (!dataStore.currentLink) return;
  updateLink([dataStore.currentLink]);
};

const onAttributeChange = (value: string, key: "domId") => {
  if (dataStore.currentNode) {
    dataStore.currentNode[key] = value;
    updateNodeAttributeByHttp();
  } else if (dataStore.currentLink) {
    dataStore.currentLink[key] = value;
    updateLinkAttributeByHttp();
  }
};

const onSyncId = () => {
  const defaultMouseEvent: IMouseEvent = {
    eventType: "click",
    groupName: "",
    height: 800,
    name: "",
    resourceCode: "",
    resourceId: "",
    value: "",
    width: 1000
  };

  const syncElementId = (elements: INode[] | ILink[]) => {
    elements.forEach((element) => {
      const mouseEvent = element.mouseEvent?.length ? element.mouseEvent : [defaultMouseEvent];

      element.mouseEvent = mouseEvent.map((item) => ({
        ...item,
        value: element.domId
      }));
    });
  };

  syncElementId(dataStore.nodesSelected);
  syncElementId(dataStore.linksSelected);
  updateNodesLinks({
    nodes: dataStore.nodesSelected,
    links: dataStore.linksSelected
  });
};
</script>

<style></style>
