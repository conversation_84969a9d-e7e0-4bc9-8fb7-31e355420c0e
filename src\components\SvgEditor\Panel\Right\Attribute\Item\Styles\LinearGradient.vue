<template>
  <div class="w-full">
    <!-- 渐变方向控制 -->
    <n-flex :wrap="false" class="w-full mb-2">
      <n-input-number
        v-model:value="gradientDirection"
        class="flex-1"
        size="small"
        :min="0"
        :max="360"
        :step="1"
        placeholder="角度"
        @update:value="updateGradient"
      />
      <span class="ml-2 text-xs text-gray-400">°</span>
    </n-flex>

    <!-- 预设方向按钮 -->
    <n-flex :wrap="true" :size="[8, 8]" class="mb-2">
      <n-button
        v-for="preset in directionPresets"
        :key="preset.angle"
        size="tiny"
        :type="gradientDirection === preset.angle ? 'primary' : 'default'"
        @click="setDirection(preset.angle)"
      >
        {{ preset.label }}
      </n-button>
    </n-flex>

    <!-- 快速操作按钮 -->
    <n-flex :wrap="true" :size="[8, 8]" class="mb-2">
      <n-button size="tiny" @click="sortStops">排序</n-button>
      <n-button size="tiny" @click="reverseStops">反转</n-button>
      <n-button size="tiny" @click="resetStops">重置</n-button>
      <n-button size="tiny" @click="distributeEvenly">均匀分布</n-button>
    </n-flex>

    <!-- 颜色停止点列表 -->
    <div class="w-full">
      <!-- 渐变预览 -->
      <div
        class="gradient-preview mb-3 h-8 rounded border border-gray-600"
        :style="{ background: gradientPreview }"
      ></div>

      <!-- 颜色停止点 -->
      <div class="color-stops-container">
        <div
          v-for="(stop, index) in colorStops"
          :key="index"
          class="color-stop-item mb-2 p-2 border border-gray-600 rounded"
        >
          <n-flex :wrap="false" align="center" :size="8">
            <!-- 颜色选择器 -->
            <n-color-picker
              v-model:value="stop.color"
              size="small"
              :modes="['hex', 'rgb']"
              :show-preview="true"
              @update:value="updateGradient"
            />

            <!-- 位置输入 -->
            <n-input-number
              v-model:value="stop.offset"
              size="small"
              :min="0"
              :max="100"
              :step="1"
              :precision="1"
              :show-button="false"
              style="width: 150px"
              placeholder="位置"
              @update:value="updateGradient"
            >
              <template #suffix> % </template>
            </n-input-number>

            <!-- 删除按钮 -->
            <n-button
              size="tiny"
              type="error"
              quaternary
              :disabled="colorStops.length <= 2"
              @click="removeColorStop(index)"
            >
              <template #icon>
                <n-icon><Delete /></n-icon>
              </template>
            </n-button>
          </n-flex>
        </div>
      </div>

      <!-- 添加颜色按钮 -->
      <n-button size="small" type="primary" dashed block @click="addColorStop">
        <template #icon>
          <n-icon><Add /></n-icon>
        </template>
        添加颜色
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { Add, Delete } from "@/utils/components/icons";
import {
  generateCssGradient,
  parseCssGradient,
  validateColorStops,
  distributeStopsEvenly as distributeStopsEvenlyUtil,
  addColorStop as addColorStopUtil,
  removeColorStop as removeColorStopUtil,
  type ColorStop
} from "@/utils/tools/gradient";
import type { IGradient } from "@/types";

// 方向预设
const directionPresets = [
  { label: "↑", angle: 0 },
  { label: "↗", angle: 45 },
  { label: "→", angle: 90 },
  { label: "↘", angle: 135 },
  { label: "↓", angle: 180 },
  { label: "↙", angle: 225 },
  { label: "←", angle: 270 },
  { label: "↖", angle: 315 }
];

const props = defineProps<{
  data?: string | number;
}>();

const emit = defineEmits<{
  "update:value": [value: IGradient];
}>();

// 响应式数据
const gradientDirection = ref(0);
const colorStops = ref<ColorStop[]>([
  { color: "#ff0000", offset: 0 },
  { color: "#0000ff", offset: 100 }
]);

// 计算渐变预览
const gradientPreview = computed(() => {
  return generateCssGradient(gradientDirection.value, colorStops.value);
});

// 解析传入的渐变数据
const parseGradientData = (data: string | number | undefined) => {
  if (!data || typeof data !== "string") return;

  try {
    const parsed = JSON.parse(data);
    if (parsed.direction !== undefined) {
      gradientDirection.value = parsed.direction;
    }
    if (parsed.stops && Array.isArray(parsed.stops)) {
      colorStops.value = validateColorStops(parsed.stops);
    }
  } catch (error) {
    // 如果解析失败，尝试解析CSS渐变字符串
    const parsedGradient = parseCssGradient(data);
    if (parsedGradient) {
      gradientDirection.value = parsedGradient.direction;
      colorStops.value = validateColorStops(parsedGradient.stops);
    }
  }
};

// 设置方向
const setDirection = (angle: number) => {
  gradientDirection.value = angle;
  updateGradient();
};

// 添加颜色停止点
const addColorStop = () => {
  colorStops.value = addColorStopUtil(colorStops.value, "#ffffff");
  updateGradient();
};

// 删除颜色停止点
const removeColorStop = (index: number) => {
  colorStops.value = removeColorStopUtil(colorStops.value, index);
  updateGradient();
};

// 更新渐变
const updateGradient = () => {
  const gradientData = {
    direction: gradientDirection.value,
    stops: colorStops.value,
    css: gradientPreview.value
  };
  emit("update:value", gradientData);
};

// 排序停止点
const sortStops = () => {
  colorStops.value.sort((a, b) => a.offset - b.offset);
  updateGradient();
};

// 反转停止点
const reverseStops = () => {
  colorStops.value.forEach((stop) => {
    stop.offset = 100 - stop.offset;
  });
  colorStops.value.reverse();
  updateGradient();
};

// 重置停止点
const resetStops = () => {
  colorStops.value = [
    { color: "#ff0000", offset: 0 },
    { color: "#0000ff", offset: 100 }
  ];
  updateGradient();
};

// 均匀分布停止点
const distributeEvenly = () => {
  colorStops.value = distributeStopsEvenlyUtil(colorStops.value);
  updateGradient();
};

// 监听props变化
watch(
  () => props.data,
  (newData) => {
    parseGradientData(newData);
  },
  { immediate: true }
);

onMounted(() => {
  parseGradientData(props.data);
});
</script>

<style scoped>
.gradient-preview {
  border: 1px solid #444;
  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%),
    linear-gradient(-45deg, #ccc 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #ccc 75%),
    linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 8px 8px;
  background-position:
    0 0,
    0 4px,
    4px -4px,
    -4px 0px;
}

.color-stops-container {
  max-height: 300px;
  overflow-y: auto;
}

.color-stop-item {
  background: rgba(255, 255, 255, 0.05);
}

.color-stop-item:hover {
  background: rgba(255, 255, 255, 0.1);
}
</style>
