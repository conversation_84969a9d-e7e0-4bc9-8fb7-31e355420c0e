{"name": "topo-editor", "version": "0.1.4", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:ts": "vite build --mode ts", "release": "release-it && run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/", "svg": "tsc  --project tsconfig.svg.json "}, "dependencies": {"@codemirror/lang-javascript": "^6.2.2", "@dagrejs/dagre": "^1.1.4", "@types/leaflet": "^1.9.15", "@unocss/reset": "^0.61.9", "axios": "^1.7.9", "codemirror": "^6.0.1", "d3": "^7.9.0", "d3-dag": "^1.1.0", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "mitt": "^3.0.1", "path-intersection": "^3.1.0", "pinia": "^2.3.0", "radash": "^12.1.0", "svg-pathdata": "^7.1.0", "vue": "^3.5.13", "vue-draggable-plus": "^0.5.6", "vue-router": "^4.5.0"}, "devDependencies": {"@release-it/conventional-changelog": "^8.0.2", "@rushstack/eslint-patch": "^1.10.4", "@tsconfig/node20": "^20.1.4", "@types/codemirror": "^5.60.15", "@types/d3": "^7.4.3", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.2", "@unocss/transformer-directives": "^0.65.4", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.4", "@vue/tsconfig": "^0.7.0", "eslint": "^9.17.0", "eslint-plugin-oxlint": "^0.15.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^9.32.0", "naive-ui": "^2.40.4", "npm-run-all2": "^6.2.6", "oxlint": "^0.15.3", "prettier": "^3.4.2", "release-it": "^17.11.0", "typescript": "~5.7.2", "unocss": "^0.65.4", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "unplugin-vue-markdown": "^0.28.0", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.0"}}