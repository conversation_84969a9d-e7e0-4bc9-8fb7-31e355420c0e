import { createRouter, createWebHistory } from "vue-router";

import HomeView from "../views/home/<USER>";

const router = createRouter({
  history: createWebHistory("/topoNew/"),
  routes: [
    {
      path: "/",
      name: "home",
      component: HomeView,
      redirect: "/editor",
      children: [
        {
          path: "/meta-icon",
          name: "MetaIcon",
          component: () => import("../views/meta/index.vue")
        },
        {
          path: "/combine",
          name: "Combine",
          component: () => import("../views/combine/index.vue")
        }
      ]
    },
    {
      path: "/editor",
      name: "Editor",
      component: () => import("../views/editor/index.vue")
    },

    {
      path: "/parse",
      name: "parse",
      component: () => import("../views/parse/index.vue")
    },
    {
      path: "/login",
      name: "Login",
      component: () => import("../views/login/index.vue")
    },
    {
      path: "/preview",
      name: "Preview",
      component: () => import("../views/preview/index.vue")
    },
    {
      path: "/gis",
      name: "Gis",
      component: () => import("../views/gis/index.vue")
    }
  ]
});

// router.beforeEach((to, from, next) => {
//   const token = getToken();
//   console.log("🚀 ~ router.beforeEach ~ token:", token);
//   if (to.name !== "Login" && !token) {
//     next({ name: "Login" });
//   } else {
//     next();
//   }
// });
export default router;
