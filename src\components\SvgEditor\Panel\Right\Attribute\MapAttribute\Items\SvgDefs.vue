<template>
  <BaseItem title="渐变">
    <LinearGradient @update:value="onUpdateGradient"></LinearGradient>
  </BaseItem>
</template>

<script setup lang="ts">
import { useMapStore } from "@/stores";

import BaseItem from "../../Item/index.vue";
import LinearGradient from "../../Item/Styles/LinearGradient.vue";
import type { IGradient } from "@/types";

const mapStore = useMapStore();

const onUpdateGradient = (val: IGradient) => {
  console.log("🚀 ~ onUpdateGradient ~ val:", val);
};
</script>
